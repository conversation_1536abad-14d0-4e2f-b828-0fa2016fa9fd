// CrowdSnap - AI-Powered Event Management
// Enhanced JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
});

function initializeApp() {
    // Header scroll effect
    initializeHeaderScroll();

    // Smooth scrolling for navigation links
    initializeSmoothScrolling();

    // Dashboard animations
    initializeDashboardAnimations();

    // App screen interactions
    initializeAppScreens();

    // Intersection Observer for fade-in animations
    initializeScrollAnimations();

    // Gallery interactions
    initializeGalleryEffects();

    // Button ripple effects
    initializeButtonEffects();

    // Parallax effects
    initializeParallaxEffects();

    // Loading animation
    initializeLoadingAnimation();
}

// Header scroll effect
function initializeHeaderScroll() {
    const header = document.querySelector('header');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(46, 55, 63, 0.98)';
            header.style.backdropFilter = 'blur(15px)';
            header.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
        } else {
            header.style.background = 'rgba(46, 55, 63, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
            header.style.boxShadow = 'none';
        }
    });
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    const navLinks = document.querySelectorAll('nav a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Dashboard animations
function initializeDashboardAnimations() {
    const dashboardCards = document.querySelectorAll('.dashboard-card');

    // Add hover effects to dashboard cards
    dashboardCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Animate dashboard on load
    setTimeout(() => {
        const mainDashboard = document.querySelector('.main-dashboard');
        if (mainDashboard) {
            mainDashboard.style.animation = 'dashboardPulse 2s ease-in-out infinite alternate';
        }
    }, 1000);
}

// App screen interactions
function initializeAppScreens() {
    const appScreens = document.querySelectorAll('.app-screen');

    appScreens.forEach(screen => {
        // Add click interaction
        screen.addEventListener('click', function() {
            // Add a subtle click effect
            this.style.transform += ' scale(0.95)';
            setTimeout(() => {
                this.style.transform = this.style.transform.replace(' scale(0.95)', '');
            }, 150);
        });

        // Add random floating animation
        const randomDelay = Math.random() * 2000;
        setTimeout(() => {
            screen.style.animation = `floatScreen 4s ease-in-out infinite alternate`;
            screen.style.animationDelay = `${Math.random() * 2}s`;
        }, randomDelay);
    });
}

// Intersection Observer for fade-in animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe all fade-in elements
    document.querySelectorAll('.fade-in').forEach(el => {
        observer.observe(el);
    });
}

// Gallery interactive effects
function initializeGalleryEffects() {
    document.querySelectorAll('.gallery-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) rotateY(5deg)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotateY(0deg)';
        });
    });
}

// Button ripple effects
function initializeButtonEffects() {
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255,255,255,0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Parallax effects
function initializeParallaxEffects() {
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero');
        if (hero) {
            hero.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
}

// Loading animation
function initializeLoadingAnimation() {
    window.addEventListener('load', () => {
        document.body.style.opacity = '0';
        setTimeout(() => {
            document.body.style.transition = 'opacity 0.5s ease';
            document.body.style.opacity = '1';
        }, 100);
    });
}

// Add CSS animations dynamically
function addDynamicStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes dashboardPulse {
            0% { box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3); }
            100% { box-shadow: 0 30px 60px rgba(173, 217, 41, 0.2); }
        }

        @keyframes floatScreen {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-10px); }
        }

        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// Initialize dynamic styles
addDynamicStyles();

// Utility function for future features
function showNotification(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);
    // This can be expanded to show actual notifications in the UI
}

// Export functions for potential future use
window.CrowdSnap = {
    showNotification,
    initializeApp
};